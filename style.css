/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Georgia', 'Times New Roman', serif;
    line-height: 1.6;
    color: #333;
    background-color: #FEFEFE;
    font-size: 16px;
}

/* 头部样式 */
header {
    background-color: #FFF;
    padding: 1rem 0;
    border-bottom: 2px solid #E0E0E0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logo h1 {
    text-align: center;
    color: #4682B4;
    font-size: 2rem;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

/* 主要内容区域 */
main {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

/* 英雄区域 */
.hero-section {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, #F0F8FF 0%, #E6F3FF 100%);
    border-radius: 15px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.hero-section h1 {
    font-size: 2.5rem;
    color: #2C3E50;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.hero-section h2 {
    font-size: 1.5rem;
    color: #7B68EE;
    margin-bottom: 2rem;
    font-style: italic;
}

.hero-image {
    margin: 2rem 0;
}

.intro {
    font-size: 1.1rem;
    color: #555;
    max-width: 600px;
    margin: 0 auto;
    background-color: rgba(255,255,255,0.8);
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #7B68EE;
}

/* 概念区域 */
.concept-section {
    margin-bottom: 4rem;
    padding: 2rem;
    background-color: #FAFAFA;
    border-radius: 15px;
    border: 2px solid #E0E0E0;
    position: relative;
}

.concept-section::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #FFB6C1, #87CEEB, #98FB98, #DDA0DD);
    border-radius: 15px;
    z-index: -1;
    opacity: 0.3;
}

.concept-section h2 {
    font-size: 2rem;
    color: #2C3E50;
    margin-bottom: 0.5rem;
    text-align: center;
}

.concept-section h3 {
    font-size: 1.3rem;
    color: #7B68EE;
    margin-bottom: 2rem;
    text-align: center;
    font-style: italic;
}

.concept-section h4 {
    font-size: 1.2rem;
    color: #4682B4;
    margin: 1.5rem 0 1rem 0;
    border-bottom: 2px solid #E0E0E0;
    padding-bottom: 0.5rem;
}

/* 视觉元素 */
.concept-visual {
    text-align: center;
    margin: 2rem 0;
    padding: 1rem;
    background-color: #FFF;
    border-radius: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.concept-visual svg {
    max-width: 100%;
    height: auto;
}

/* 解释文本 */
.explanation {
    background-color: #FFF;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.explanation p {
    margin-bottom: 1rem;
    font-size: 1.05rem;
    line-height: 1.7;
}

.explanation ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.explanation li {
    margin-bottom: 0.8rem;
    line-height: 1.6;
}

.explanation li strong {
    color: #4682B4;
}

/* 类比框 */
.analogy-box {
    background: linear-gradient(135deg, #FFF8DC 0%, #F0E68C 100%);
    border: 2px solid #DAA520;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 2rem 0;
    position: relative;
}

.analogy-box::before {
    content: '💡';
    position: absolute;
    top: -15px;
    left: 20px;
    background-color: #FFF8DC;
    padding: 0 10px;
    font-size: 1.5rem;
}

.analogy-box h4 {
    color: #B8860B;
    margin-bottom: 1rem;
    border: none;
    padding: 0;
}

.analogy-box p {
    margin-bottom: 0.5rem;
    color: #8B4513;
}

/* SVG动画 */
.animated-svg .brain {
    animation: pulse 3s ease-in-out infinite;
}

.animated-svg .sft-gear {
    animation: rotate 4s linear infinite;
    transform-origin: 150px 120px;
}

.animated-svg .cot-chain {
    animation: bounce 2s ease-in-out infinite;
}

.animated-svg .connection {
    animation: dash 2s linear infinite;
}

.animated-svg .thought-bubble {
    animation: float 3s ease-in-out infinite;
}

/* 动画定义 */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

@keyframes dash {
    0% { stroke-dashoffset: 0; }
    100% { stroke-dashoffset: 20; }
}

@keyframes float {
    0%, 100% { transform: translateY(0); opacity: 0.7; }
    50% { transform: translateY(-5px); opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem;
    }

    .hero-section h2 {
        font-size: 1.2rem;
    }

    .concept-section {
        padding: 1rem;
    }

    .concept-visual svg {
        width: 100%;
        height: auto;
    }

    main {
        padding: 1rem 0.5rem;
    }
}

/* 对比区域样式 */
.comparison-section {
    margin-bottom: 4rem;
    padding: 2rem;
    background: linear-gradient(135deg, #F8F8FF 0%, #E6E6FA 100%);
    border-radius: 15px;
    border: 2px solid #DDA0DD;
}

.comparison-table {
    margin-top: 2rem;
    overflow-x: auto;
}

.comparison-table table {
    width: 100%;
    border-collapse: collapse;
    background-color: #FFF;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.comparison-table th,
.comparison-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #E0E0E0;
}

.comparison-table th {
    background-color: #4682B4;
    color: white;
    font-weight: bold;
}

.comparison-table tr:nth-child(even) {
    background-color: #F8F8FF;
}

.comparison-table tr:hover {
    background-color: #E6F3FF;
}

/* 实际应用区域样式 */
.practical-section {
    margin-bottom: 4rem;
    padding: 2rem;
    background: linear-gradient(135deg, #F0FFFF 0%, #E0FFFF 100%);
    border-radius: 15px;
    border: 2px solid #20B2AA;
}

.application-examples {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.example-card {
    background-color: #FFF;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.example-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.15);
}

.sft-example {
    border-left: 5px solid #4682B4;
}

.cot-example {
    border-left: 5px solid #228B22;
}

.example-card h3 {
    color: #2C3E50;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.example-card h4 {
    color: #4682B4;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.example-dialogue {
    background-color: #F8F8FF;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    border-left: 3px solid #7B68EE;
}

.example-dialogue p {
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

/* 未来展望区域样式 */
.future-section {
    margin-bottom: 4rem;
    padding: 2rem;
    background: linear-gradient(135deg, #FFF5EE 0%, #FFE4E1 100%);
    border-radius: 15px;
    border: 2px solid #FF6347;
}

.future-trends h3 {
    color: #2C3E50;
    margin-bottom: 1.5rem;
    text-align: center;
}

.trend-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-top: 2rem;
}

.trend-item {
    background-color: #FFF;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.trend-item:hover {
    transform: translateY(-3px);
}

.trend-item h4 {
    color: #FF6347;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* 总结区域样式 */
.conclusion-section {
    margin-bottom: 4rem;
    padding: 2rem;
    background: linear-gradient(135deg, #FFFACD 0%, #F0E68C 100%);
    border-radius: 15px;
    border: 2px solid #DAA520;
    text-align: center;
}

.key-takeaways h3 {
    color: #2C3E50;
    margin-bottom: 2rem;
}

.takeaway-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin: 2rem 0;
}

.takeaway-item {
    background-color: #FFF;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    text-align: left;
}

.takeaway-item h4 {
    color: #B8860B;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.final-message {
    background-color: rgba(255,255,255,0.8);
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 2rem;
    border: 2px solid #DAA520;
}

.final-message p {
    font-size: 1.1rem;
    color: #8B4513;
    margin: 0;
}

/* 页脚样式 */
footer {
    background-color: #2C3E50;
    color: #FFF;
    padding: 2rem 0;
    text-align: center;
    margin-top: 4rem;
}

.footer-content p {
    margin-bottom: 0.5rem;
}

.footer-content a {
    color: #87CEEB;
    text-decoration: none;
}

.footer-content a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem;
    }

    .hero-section h2 {
        font-size: 1.2rem;
    }

    .concept-section {
        padding: 1rem;
    }

    .concept-visual svg {
        width: 100%;
        height: auto;
    }

    main {
        padding: 1rem 0.5rem;
    }

    .application-examples {
        grid-template-columns: 1fr;
    }

    .trend-grid {
        grid-template-columns: 1fr;
    }

    .takeaway-grid {
        grid-template-columns: 1fr;
    }

    .comparison-table {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .hero-section h1 {
        font-size: 1.5rem;
    }

    .concept-section h2 {
        font-size: 1.5rem;
    }

    .explanation {
        padding: 1rem;
    }

    .analogy-box {
        padding: 1rem;
    }

    .example-card {
        padding: 1rem;
    }

    .trend-item {
        padding: 1rem;
    }

    .takeaway-item {
        padding: 1rem;
    }
}
