<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI大模型的SFT和COT - 让AI更聪明的两种魔法</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <nav>
            <div class="logo">
                <h1>AI学习笔记</h1>
            </div>
        </nav>
    </header>

    <main>
        <article>
            <div class="hero-section">
                <h1>AI大模型的SFT和COT</h1>
                <h2>让AI更聪明的两种魔法</h2>
                <div class="hero-image">
                    <svg width="500" height="350" viewBox="0 0 500 350" class="animated-svg">
                        <!-- 大脑形状 -->
                        <path d="M250 70 C320 70, 380 110, 380 170 C380 230, 320 270, 250 270 C180 270, 120 230, 120 170 C120 110, 180 70, 250 70 Z"
                              fill="#FFE4B5" stroke="#8B4513" stroke-width="3" class="brain"/>

                        <!-- SFT齿轮 -->
                        <g class="sft-gear" transform="translate(200, 170)">
                            <circle cx="0" cy="0" r="30" fill="#87CEEB" stroke="#4682B4" stroke-width="3"/>
                            <!-- 齿轮齿 -->
                            <g class="gear-teeth">
                                <rect x="-3" y="-35" width="6" height="10" fill="#4682B4"/>
                                <rect x="-3" y="25" width="6" height="10" fill="#4682B4"/>
                                <rect x="-35" y="-3" width="10" height="6" fill="#4682B4"/>
                                <rect x="25" y="-3" width="10" height="6" fill="#4682B4"/>
                                <rect x="-25" y="-25" width="8" height="8" fill="#4682B4" transform="rotate(45)"/>
                                <rect x="17" y="-25" width="8" height="8" fill="#4682B4" transform="rotate(45)"/>
                                <rect x="-25" y="17" width="8" height="8" fill="#4682B4" transform="rotate(45)"/>
                                <rect x="17" y="17" width="8" height="8" fill="#4682B4" transform="rotate(45)"/>
                            </g>
                            <text x="0" y="6" text-anchor="middle" font-size="14" font-weight="bold" fill="#FFF">SFT</text>
                        </g>

                        <!-- COT链条 -->
                        <g class="cot-chain" transform="translate(300, 170)">
                            <circle cx="0" cy="0" r="30" fill="#98FB98" stroke="#228B22" stroke-width="3"/>
                            <!-- 链条环节 -->
                            <g class="chain-links">
                                <circle cx="-15" cy="-15" r="8" fill="none" stroke="#228B22" stroke-width="3"/>
                                <circle cx="15" cy="-15" r="8" fill="none" stroke="#228B22" stroke-width="3"/>
                                <circle cx="-15" cy="15" r="8" fill="none" stroke="#228B22" stroke-width="3"/>
                                <circle cx="15" cy="15" r="8" fill="none" stroke="#228B22" stroke-width="3"/>
                                <line x1="-7" y1="-15" x2="7" y2="-15" stroke="#228B22" stroke-width="3"/>
                                <line x1="-7" y1="15" x2="7" y2="15" stroke="#228B22" stroke-width="3"/>
                                <line x1="-15" y1="-7" x2="-15" y2="7" stroke="#228B22" stroke-width="3"/>
                                <line x1="15" y1="-7" x2="15" y2="7" stroke="#228B22" stroke-width="3"/>
                            </g>
                            <text x="0" y="6" text-anchor="middle" font-size="14" font-weight="bold" fill="#FFF">COT</text>
                        </g>

                        <!-- 连接线 -->
                        <line x1="230" y1="170" x2="270" y2="170" stroke="#666" stroke-width="3" stroke-dasharray="8,4" class="connection"/>
                        <text x="250" y="160" text-anchor="middle" font-size="12" fill="#666">协同工作</text>

                        <!-- 思考泡泡 -->
                        <circle cx="250" cy="120" r="12" fill="#FFF" stroke="#666" stroke-width="2" class="thought-bubble"/>
                        <circle cx="265" cy="105" r="9" fill="#FFF" stroke="#666" stroke-width="2" class="thought-bubble"/>
                        <circle cx="280" cy="90" r="6" fill="#FFF" stroke="#666" stroke-width="2" class="thought-bubble"/>

                        <!-- 智慧火花 -->
                        <g class="wisdom-sparks">
                            <text x="150" y="100" font-size="20" class="spark">💡</text>
                            <text x="350" y="100" font-size="20" class="spark">⚡</text>
                            <text x="180" y="250" font-size="16" class="spark">✨</text>
                            <text x="320" y="250" font-size="16" class="spark">✨</text>
                        </g>

                        <!-- 标签 -->
                        <text x="200" y="320" text-anchor="middle" font-size="12" fill="#4682B4" font-weight="bold">监督微调</text>
                        <text x="300" y="320" text-anchor="middle" font-size="12" fill="#228B22" font-weight="bold">思维链推理</text>
                    </svg>
                </div>
                <p class="intro">想象一下，如果AI是一个学生，那么SFT就像是请了一位好老师，而COT就像是教会了它如何思考。让我们一起探索这两种让AI变得更聪明的神奇方法！</p>
            </div>

            <section class="concept-section">
                <h2>什么是SFT？</h2>
                <h3>监督微调 - AI的私人教练</h3>

                <div class="concept-visual">
                    <svg width="500" height="250" viewBox="0 0 500 250">
                        <!-- 老师 -->
                        <g class="teacher">
                            <circle cx="100" cy="80" r="30" fill="#FFB6C1" stroke="#8B4513" stroke-width="2"/>
                            <circle cx="90" cy="75" r="3" fill="#000"/>
                            <circle cx="110" cy="75" r="3" fill="#000"/>
                            <path d="M85 90 Q100 100 115 90" stroke="#000" stroke-width="2" fill="none"/>
                            <text x="100" y="130" text-anchor="middle" font-size="14" font-weight="bold">老师</text>
                        </g>

                        <!-- 箭头 -->
                        <path d="M150 100 L200 100" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="175" y="90" text-anchor="middle" font-size="12">示例</text>

                        <!-- AI学生 -->
                        <g class="ai-student">
                            <rect x="220" y="60" width="60" height="40" rx="10" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
                            <circle cx="235" cy="75" r="3" fill="#000"/>
                            <circle cx="255" cy="75" r="3" fill="#000"/>
                            <text x="250" y="130" text-anchor="middle" font-size="14" font-weight="bold">AI</text>
                        </g>

                        <!-- 箭头 -->
                        <path d="M300 100 L350 100" stroke="#666" stroke-width="3" marker-end="url(#arrowhead)"/>
                        <text x="325" y="90" text-anchor="middle" font-size="12">学习</text>

                        <!-- 改进后的AI -->
                        <g class="improved-ai">
                            <rect x="370" y="60" width="60" height="40" rx="10" fill="#98FB98" stroke="#228B22" stroke-width="2"/>
                            <circle cx="385" cy="75" r="3" fill="#000"/>
                            <circle cx="405" cy="75" r="3" fill="#000"/>
                            <path d="M380 85 Q395 90 410 85" stroke="#000" stroke-width="2" fill="none"/>
                            <text x="400" y="130" text-anchor="middle" font-size="14" font-weight="bold">聪明AI</text>
                        </g>

                        <!-- 示例对话框 -->
                        <g class="example-box">
                            <rect x="50" y="150" width="400" height="80" rx="10" fill="#FFF8DC" stroke="#DDD" stroke-width="1"/>
                            <text x="60" y="170" font-size="12" font-weight="bold">示例对话：</text>
                            <text x="60" y="190" font-size="11">人类：请帮我写一封感谢信</text>
                            <text x="60" y="205" font-size="11">AI：亲爱的朋友，感谢您的帮助...</text>
                            <text x="60" y="220" font-size="11">✓ 这样回答很好！</text>
                        </g>

                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <div class="explanation">
                    <p><strong>SFT（Supervised Fine-Tuning）</strong>就像是给AI请了一位经验丰富的私人教练。</p>

                    <h4>🎯 SFT是如何工作的？</h4>
                    <ul>
                        <li><strong>准备教材</strong>：人类专家准备大量高质量的问答对话示例</li>
                        <li><strong>示范教学</strong>：向AI展示"遇到这种问题，应该这样回答"</li>
                        <li><strong>反复练习</strong>：AI通过学习这些示例，逐渐掌握正确的回答方式</li>
                        <li><strong>能力提升</strong>：最终AI学会了如何给出更有用、更准确的回答</li>
                    </ul>

                    <div class="analogy-box">
                        <h4>🏀 生活中的类比</h4>
                        <p>就像篮球教练教新手投篮一样：</p>
                        <p>教练示范标准动作 → 学员模仿练习 → 纠正错误 → 技术提高</p>
                        <p>SFT就是AI的"标准动作"训练！</p>
                    </div>
                </div>
            </section>

            <section class="concept-section">
                <h2>什么是COT？</h2>
                <h3>思维链 - AI的思考艺术</h3>

                <div class="concept-visual">
                    <svg width="500" height="300" viewBox="0 0 500 300">
                        <!-- 问题 -->
                        <g class="problem">
                            <rect x="20" y="50" width="80" height="40" rx="5" fill="#FFB6C1" stroke="#8B4513" stroke-width="2"/>
                            <text x="60" y="75" text-anchor="middle" font-size="12" font-weight="bold">复杂问题</text>
                        </g>

                        <!-- 思维链步骤 -->
                        <g class="thinking-chain">
                            <!-- 步骤1 -->
                            <circle cx="150" cy="70" r="20" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
                            <text x="150" y="75" text-anchor="middle" font-size="10">步骤1</text>

                            <!-- 箭头1 -->
                            <path d="M170 70 L190 70" stroke="#666" stroke-width="2" marker-end="url(#arrowhead2)"/>

                            <!-- 步骤2 -->
                            <circle cx="220" cy="70" r="20" fill="#98FB98" stroke="#228B22" stroke-width="2"/>
                            <text x="220" y="75" text-anchor="middle" font-size="10">步骤2</text>

                            <!-- 箭头2 -->
                            <path d="M240 70 L260 70" stroke="#666" stroke-width="2" marker-end="url(#arrowhead2)"/>

                            <!-- 步骤3 -->
                            <circle cx="290" cy="70" r="20" fill="#DDA0DD" stroke="#8B008B" stroke-width="2"/>
                            <text x="290" y="75" text-anchor="middle" font-size="10">步骤3</text>

                            <!-- 箭头3 -->
                            <path d="M310 70 L330 70" stroke="#666" stroke-width="2" marker-end="url(#arrowhead2)"/>
                        </g>

                        <!-- 答案 -->
                        <g class="answer">
                            <rect x="350" y="50" width="80" height="40" rx="5" fill="#FFD700" stroke="#B8860B" stroke-width="2"/>
                            <text x="390" y="75" text-anchor="middle" font-size="12" font-weight="bold">正确答案</text>
                        </g>

                        <!-- 详细思考过程 -->
                        <g class="detailed-thinking">
                            <rect x="50" y="130" width="400" height="150" rx="10" fill="#F0F8FF" stroke="#B0C4DE" stroke-width="2"/>
                            <text x="60" y="150" font-size="14" font-weight="bold">COT思考过程示例：</text>

                            <text x="60" y="175" font-size="12" font-weight="bold">问题：小明有15个苹果，给了小红1/3，还剩多少个？</text>

                            <text x="60" y="200" font-size="11" fill="#4682B4">步骤1：理解问题 - 小明原有15个苹果</text>
                            <text x="60" y="215" font-size="11" fill="#228B22">步骤2：计算给出的数量 - 15 × 1/3 = 5个</text>
                            <text x="60" y="230" font-size="11" fill="#8B008B">步骤3：计算剩余 - 15 - 5 = 10个</text>
                            <text x="60" y="250" font-size="11" fill="#B8860B" font-weight="bold">答案：小明还剩10个苹果</text>

                            <text x="60" y="270" font-size="10" fill="#666">💡 通过分步思考，AI能够处理更复杂的问题！</text>
                        </g>

                        <defs>
                            <marker id="arrowhead2" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <div class="explanation">
                    <p><strong>COT（Chain of Thought）</strong>就像是教会AI如何"慢慢想"，一步一步地解决问题。</p>

                    <h4>🧠 COT是如何工作的？</h4>
                    <ul>
                        <li><strong>分解问题</strong>：将复杂问题拆分成多个简单步骤</li>
                        <li><strong>逐步推理</strong>：AI学会展示每一步的思考过程</li>
                        <li><strong>逻辑连贯</strong>：确保每个步骤之间有清晰的逻辑关系</li>
                        <li><strong>得出结论</strong>：通过完整的推理链得到最终答案</li>
                    </ul>

                    <div class="analogy-box">
                        <h4>🔍 生活中的类比</h4>
                        <p>就像侦探破案一样：</p>
                        <p>发现线索 → 分析证据 → 推理过程 → 找出真相</p>
                        <p>COT让AI也能像侦探一样，一步步推理出答案！</p>
                    </div>
                </div>
            </section>

            <section class="comparison-section">
                <h2>SFT vs COT：两种方法的对比</h2>

                <div class="comparison-visual">
                    <svg width="600" height="400" viewBox="0 0 600 400">
                        <!-- SFT部分 -->
                        <g class="sft-section">
                            <rect x="50" y="50" width="200" height="300" rx="15" fill="#E6F3FF" stroke="#4682B4" stroke-width="3"/>
                            <text x="150" y="80" text-anchor="middle" font-size="18" font-weight="bold" fill="#4682B4">SFT</text>
                            <text x="150" y="100" text-anchor="middle" font-size="12" fill="#4682B4">监督微调</text>

                            <!-- SFT特点 -->
                            <circle cx="80" cy="130" r="5" fill="#87CEEB"/>
                            <text x="95" y="135" font-size="11">需要大量示例</text>

                            <circle cx="80" cy="160" r="5" fill="#87CEEB"/>
                            <text x="95" y="165" font-size="11">提升回答质量</text>

                            <circle cx="80" cy="190" r="5" fill="#87CEEB"/>
                            <text x="95" y="195" font-size="11">学习正确格式</text>

                            <circle cx="80" cy="220" r="5" fill="#87CEEB"/>
                            <text x="95" y="225" font-size="11">适合对话任务</text>

                            <circle cx="80" cy="250" r="5" fill="#87CEEB"/>
                            <text x="95" y="255" font-size="11">一次性训练</text>

                            <!-- SFT图标 -->
                            <g transform="translate(150, 290)">
                                <circle cx="0" cy="0" r="20" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
                                <text x="0" y="5" text-anchor="middle" font-size="16">📚</text>
                            </g>
                        </g>

                        <!-- VS -->
                        <g class="vs-section">
                            <circle cx="300" cy="200" r="30" fill="#FFD700" stroke="#B8860B" stroke-width="3"/>
                            <text x="300" y="210" text-anchor="middle" font-size="20" font-weight="bold">VS</text>
                        </g>

                        <!-- COT部分 -->
                        <g class="cot-section">
                            <rect x="350" y="50" width="200" height="300" rx="15" fill="#F0FFF0" stroke="#228B22" stroke-width="3"/>
                            <text x="450" y="80" text-anchor="middle" font-size="18" font-weight="bold" fill="#228B22">COT</text>
                            <text x="450" y="100" text-anchor="middle" font-size="12" fill="#228B22">思维链推理</text>

                            <!-- COT特点 -->
                            <circle cx="380" cy="130" r="5" fill="#98FB98"/>
                            <text x="395" y="135" font-size="11">分步骤思考</text>

                            <circle cx="380" cy="160" r="5" fill="#98FB98"/>
                            <text x="395" y="165" font-size="11">提升推理能力</text>

                            <circle cx="380" cy="190" r="5" fill="#98FB98"/>
                            <text x="395" y="195" font-size="11">展示思考过程</text>

                            <circle cx="380" cy="220" r="5" fill="#98FB98"/>
                            <text x="395" y="225" font-size="11">适合复杂问题</text>

                            <circle cx="380" cy="250" r="5" fill="#98FB98"/>
                            <text x="395" y="255" font-size="11">即时应用</text>

                            <!-- COT图标 -->
                            <g transform="translate(450, 290)">
                                <circle cx="0" cy="0" r="20" fill="#98FB98" stroke="#228B22" stroke-width="2"/>
                                <text x="0" y="5" text-anchor="middle" font-size="16">🧠</text>
                            </g>
                        </g>
                    </svg>
                </div>

                <div class="comparison-table">
                    <table>
                        <thead>
                            <tr>
                                <th>对比维度</th>
                                <th>SFT (监督微调)</th>
                                <th>COT (思维链)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>主要作用</strong></td>
                                <td>提升回答质量和格式</td>
                                <td>增强推理和思考能力</td>
                            </tr>
                            <tr>
                                <td><strong>实现方式</strong></td>
                                <td>用示例数据重新训练</td>
                                <td>在提示中引导分步思考</td>
                            </tr>
                            <tr>
                                <td><strong>资源需求</strong></td>
                                <td>需要大量计算资源</td>
                                <td>几乎不需要额外资源</td>
                            </tr>
                            <tr>
                                <td><strong>适用场景</strong></td>
                                <td>对话、写作、格式化任务</td>
                                <td>数学、逻辑、复杂推理</td>
                            </tr>
                            <tr>
                                <td><strong>效果持续性</strong></td>
                                <td>永久改变模型行为</td>
                                <td>仅在当次对话中有效</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="practical-section">
                <h2>实际应用：SFT和COT在生活中的例子</h2>

                <div class="application-examples">
                    <div class="example-card sft-example">
                        <h3>🤖 SFT的应用场景</h3>
                        <div class="example-content">
                            <h4>客服聊天机器人</h4>
                            <p>通过SFT训练，AI学会了：</p>
                            <ul>
                                <li>礼貌的问候语</li>
                                <li>专业的回答方式</li>
                                <li>合适的语气和措辞</li>
                                <li>标准的处理流程</li>
                            </ul>

                            <div class="example-dialogue">
                                <p><strong>用户：</strong>我的订单什么时候能到？</p>
                                <p><strong>AI：</strong>您好！我很乐意帮您查询订单状态。请提供您的订单号，我会立即为您查询配送信息。</p>
                            </div>
                        </div>
                    </div>

                    <div class="example-card cot-example">
                        <h3>🧠 COT的应用场景</h3>
                        <div class="example-content">
                            <h4>数学题解答助手</h4>
                            <p>使用COT方法，AI能够：</p>
                            <ul>
                                <li>分解复杂问题</li>
                                <li>展示解题步骤</li>
                                <li>解释每步原理</li>
                                <li>检查答案合理性</li>
                            </ul>

                            <div class="example-dialogue">
                                <p><strong>问题：</strong>一个班级有30名学生，其中60%是女生，女生中有1/4戴眼镜，问戴眼镜的女生有多少人？</p>
                                <p><strong>AI思考过程：</strong></p>
                                <p>步骤1：计算女生总数 = 30 × 60% = 18人</p>
                                <p>步骤2：计算戴眼镜的女生 = 18 × 1/4 = 4.5人</p>
                                <p>步骤3：由于人数必须是整数，答案是4或5人</p>
                                <p><strong>答案：</strong>戴眼镜的女生有4-5人（取决于具体情况）</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="future-section">
                <h2>未来展望：SFT和COT的发展趋势</h2>

                <div class="future-visual">
                    <svg width="500" height="250" viewBox="0 0 500 250">
                        <!-- 时间轴 -->
                        <line x1="50" y1="200" x2="450" y2="200" stroke="#666" stroke-width="3"/>

                        <!-- 现在 -->
                        <g class="timeline-point">
                            <circle cx="150" cy="200" r="8" fill="#4682B4"/>
                            <text x="150" y="230" text-anchor="middle" font-size="12" font-weight="bold">现在</text>
                            <rect x="100" y="120" width="100" height="60" rx="5" fill="#E6F3FF" stroke="#4682B4" stroke-width="2"/>
                            <text x="150" y="140" text-anchor="middle" font-size="10">SFT + COT</text>
                            <text x="150" y="155" text-anchor="middle" font-size="10">分别使用</text>
                            <text x="150" y="170" text-anchor="middle" font-size="10">各有优势</text>
                        </g>

                        <!-- 未来 -->
                        <g class="timeline-point">
                            <circle cx="350" cy="200" r="8" fill="#228B22"/>
                            <text x="350" y="230" text-anchor="middle" font-size="12" font-weight="bold">未来</text>
                            <rect x="300" y="100" width="100" height="80" rx="5" fill="#F0FFF0" stroke="#228B22" stroke-width="2"/>
                            <text x="350" y="120" text-anchor="middle" font-size="10">智能融合</text>
                            <text x="350" y="135" text-anchor="middle" font-size="10">自动选择</text>
                            <text x="350" y="150" text-anchor="middle" font-size="10">最佳策略</text>
                            <text x="350" y="165" text-anchor="middle" font-size="10">更强AI</text>
                        </g>

                        <!-- 箭头 -->
                        <path d="M200 200 L300 200" stroke="#666" stroke-width="2" marker-end="url(#arrowhead3)"/>
                        <text x="250" y="190" text-anchor="middle" font-size="11">发展</text>

                        <!-- 创新点 -->
                        <g class="innovation-points">
                            <circle cx="250" cy="150" r="4" fill="#FFD700"/>
                            <text x="250" y="140" text-anchor="middle" font-size="9">自适应COT</text>

                            <circle cx="280" cy="130" r="4" fill="#FFD700"/>
                            <text x="280" y="120" text-anchor="middle" font-size="9">多模态SFT</text>

                            <circle cx="320" cy="160" r="4" fill="#FFD700"/>
                            <text x="320" y="150" text-anchor="middle" font-size="9">实时学习</text>
                        </g>

                        <defs>
                            <marker id="arrowhead3" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                            </marker>
                        </defs>
                    </svg>
                </div>

                <div class="future-trends">
                    <h3>🚀 发展趋势</h3>
                    <div class="trend-grid">
                        <div class="trend-item">
                            <h4>🔄 自适应融合</h4>
                            <p>AI将学会根据问题类型自动选择使用SFT还是COT，或者两者结合使用。</p>
                        </div>

                        <div class="trend-item">
                            <h4>🎯 个性化定制</h4>
                            <p>针对不同用户和场景，AI将提供个性化的SFT训练和COT推理策略。</p>
                        </div>

                        <div class="trend-item">
                            <h4>⚡ 实时优化</h4>
                            <p>AI将能够在使用过程中实时学习和优化，不断改进SFT效果和COT质量。</p>
                        </div>

                        <div class="trend-item">
                            <h4>🌐 多模态扩展</h4>
                            <p>SFT和COT将扩展到图像、音频、视频等多种模态，实现更全面的AI能力。</p>
                        </div>
                    </div>
                </div>
            </section>

            <section class="conclusion-section">
                <h2>总结</h2>

                <div class="conclusion-visual">
                    <svg width="400" height="200" viewBox="0 0 400 200">
                        <!-- 中心圆 -->
                        <circle cx="200" cy="100" r="60" fill="#FFE4B5" stroke="#8B4513" stroke-width="3"/>
                        <text x="200" y="90" text-anchor="middle" font-size="14" font-weight="bold">更强大的</text>
                        <text x="200" y="110" text-anchor="middle" font-size="14" font-weight="bold">AI系统</text>

                        <!-- SFT -->
                        <g class="conclusion-sft">
                            <circle cx="100" cy="100" r="40" fill="#87CEEB" stroke="#4682B4" stroke-width="2"/>
                            <text x="100" y="95" text-anchor="middle" font-size="12" font-weight="bold">SFT</text>
                            <text x="100" y="110" text-anchor="middle" font-size="10">提升质量</text>
                            <line x1="140" y1="100" x2="160" y2="100" stroke="#666" stroke-width="2"/>
                        </g>

                        <!-- COT -->
                        <g class="conclusion-cot">
                            <circle cx="300" cy="100" r="40" fill="#98FB98" stroke="#228B22" stroke-width="2"/>
                            <text x="300" y="95" text-anchor="middle" font-size="12" font-weight="bold">COT</text>
                            <text x="300" y="110" text-anchor="middle" font-size="10">增强推理</text>
                            <line x1="260" y1="100" x2="240" y2="100" stroke="#666" stroke-width="2"/>
                        </g>

                        <!-- 装饰元素 -->
                        <g class="sparkles">
                            <text x="150" y="50" font-size="20">✨</text>
                            <text x="250" y="50" font-size="20">✨</text>
                            <text x="120" y="160" font-size="16">⭐</text>
                            <text x="280" y="160" font-size="16">⭐</text>
                        </g>
                    </svg>
                </div>

                <div class="key-takeaways">
                    <h3>🎯 关键要点</h3>
                    <div class="takeaway-grid">
                        <div class="takeaway-item">
                            <h4>📚 SFT - 学习标准</h4>
                            <p>通过大量优质示例训练，让AI学会正确的回答方式和格式，就像给AI请了一位好老师。</p>
                        </div>

                        <div class="takeaway-item">
                            <h4>🧠 COT - 学会思考</h4>
                            <p>教会AI分步骤思考问题，展示推理过程，让AI能够处理更复杂的逻辑问题。</p>
                        </div>

                        <div class="takeaway-item">
                            <h4>🤝 协同作用</h4>
                            <p>SFT和COT各有优势，结合使用能够创造出更智能、更可靠的AI系统。</p>
                        </div>

                        <div class="takeaway-item">
                            <h4>🚀 未来可期</h4>
                            <p>随着技术发展，这两种方法将更加智能化和个性化，为我们带来更好的AI体验。</p>
                        </div>
                    </div>
                </div>

                <div class="final-message">
                    <p>🌟 <strong>记住：</strong>SFT让AI说得更好，COT让AI想得更深。两者结合，就是我们迈向更智能AI的重要一步！</p>
                </div>
            </section>
        </article>
    </main>

    <footer>
        <div class="footer-content">
            <p>© 2024 AI学习笔记 | 让复杂的AI概念变得简单易懂</p>
            <p>🎨 灵感来源于 <a href="https://ralphammer.com" target="_blank">Ralph Ammer</a> 的优秀设计风格</p>
        </div>
    </footer>
</body>
</html>
